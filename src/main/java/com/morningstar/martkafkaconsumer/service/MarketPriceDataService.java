package com.morningstar.martkafkaconsumer.service;

import com.morningstar.data.domain.ics.eod.enriched.CopyOverReasonEnum;
import com.morningstar.data.domain.ics.eod.enriched.MarketPriceDetail;
import com.morningstar.data.domain.proto.TsDataProtoBuf;
import com.morningstar.martkafkaconsumer.config.MarketPriceDataConfig;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.util.Lz4Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;


@Slf4j
@Service
public class MarketPriceDataService {

    private final RedisTsRepo redisTsRepo;
    private final MarketPriceDataConfig config;

    @Autowired
    public MarketPriceDataService(RedisTsRepo redisTsRepo, MarketPriceDataConfig config) {
        this.redisTsRepo = redisTsRepo;
        this.config = config;
    }

    public void storeMarketPriceDetail(MarketPriceDetail marketPriceDetail) {
        // Input validation
        if (marketPriceDetail == null) {
            log.warn("MarketPriceDataService: MarketPriceDetail is null, skipping storage");
            return;
        }

        String investmentId = marketPriceDetail.getPerformanceId();
        LocalDate date = marketPriceDetail.getDate();

        // Validate required fields
        if (investmentId == null || date == null) {
            log.warn("MarketPriceDataService: Missing required fields - investmentId: {}, date: {}, skipping storage",
                    investmentId, date);
            return;
        }

        log.info("MarketPriceDataService: Storing market price data for investmentId: {}, date: {}",
                investmentId, date);

        try {
            // Store each data point using configured mappings
            Map<String, String> dataPointMapping = config.getDataPointMapping();

            // Get copyOverReason for use in mergeData
            CopyOverReasonEnum copyOverReason = marketPriceDetail.getCopyOverReason();

            // 批量准备所有数据点
            Map<String, Object> dataPoints = Map.of(
                dataPointMapping.get("high"), getDoubleValue(marketPriceDetail.getHigh()),
                dataPointMapping.get("low"), getDoubleValue(marketPriceDetail.getLow()),
                dataPointMapping.get("open"), getDoubleValue(marketPriceDetail.getOpen()),
                dataPointMapping.get("close"), getDoubleValue(marketPriceDetail.getClose()),
                dataPointMapping.get("tradedVolume"), getDoubleValue(marketPriceDetail.getTradedVolume())
            );

            storeDataPointsBatch(investmentId, dataPoints, date, copyOverReason);

        } catch (Exception e) {
            log.error("MarketPriceDataService: Failed to store market price data for investmentId: {}, date: {}",
                    investmentId, date, e);
            throw new RuntimeException("Failed to store market price data", e);
        }
    }

    /**
     * Batch store multiple data points to Redis
     */
    private void storeDataPointsBatch(String investmentId, Map<String, Object> dataPoints,
                                     LocalDate date, CopyOverReasonEnum copyOverReason) {
        try {
            // 1. Calculate Redis key and field using configuration
            int year = date.getYear();
            int keyYear = (year / config.getYearsPerKey()) * config.getYearsPerKey(); // 10-year cycle start year
            int fieldStartYear = (year / config.getYearsPerField()) * config.getYearsPerField(); // 2-year cycle start year

            String redisKey = config.getRedisKeyPrefix() + investmentId + ":" + keyYear;
            byte[] keyBytes = redisKey.getBytes(StandardCharsets.UTF_8);

            Map<byte[], byte[]> batchData = new HashMap<>();

            for (Map.Entry<String, Object> entry : dataPoints.entrySet()) {
                String dpId = entry.getKey();
                double value = (Double) entry.getValue();
                String field = dpId + ":" + fieldStartYear;
                byte[] fieldBytes = field.getBytes(StandardCharsets.UTF_8);

                // 2. Get existing data (if exists)
                TsDataProtoBuf.TSDataDouble existingData = getExistingData(keyBytes, fieldBytes, investmentId, dpId);

                // 3. Merge new data
                TsDataProtoBuf.TSDataDouble updatedData = mergeData(existingData, investmentId, dpId, value, date, copyOverReason);

                // 4. Compress and store
                byte[] serializedData = updatedData.toByteArray();
                byte[] compressedData = Lz4Util.compress(serializedData);

                batchData.put(fieldBytes, compressedData);
            }

            // 5. Batch store to Redis without expiration
            setHashValueBatch(keyBytes, batchData);

            log.debug("MarketPriceDataService: Batch stored {} data points for key: {}",
                    batchData.size(), redisKey);

        } catch (Exception e) {
            log.error("MarketPriceDataService: Error in batch storing data points for investmentId: {}, date: {}",
                    investmentId, date, e);
            throw new RuntimeException("Failed to batch store time series data", e);
        }
    }

    /**
     * Get existing data from Redis
     */
    private TsDataProtoBuf.TSDataDouble getExistingData(byte[] keyBytes, byte[] fieldBytes,
                                                        String investmentId, String dpId) {

        return redisTsRepo.getHashValue(keyBytes, Collections.singletonList(fieldBytes))
                .next()
                .flatMap(compressedData -> {
                    try {
                        byte[] decompressed = Lz4Util.decompress(compressedData);
                        return Mono.just(TsDataProtoBuf.TSDataDouble.parseFrom(decompressed));
                    } catch (Exception e) {
                        log.debug("MarketPriceDataService: Failed to decompress existing data, creating new", e);
                        return Mono.empty();
                    }
                })
                .blockOptional()
                .orElse(createEmptyTSData(investmentId, dpId));

    }

    /**
     * Create empty TSDataDouble object
     */
    private TsDataProtoBuf.TSDataDouble createEmptyTSData(String investmentId, String dpId) {
        return TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId(investmentId)
                .setDpId(dpId)
                .build();
    }

    /**
     * Merge new data with existing data
     */
    private TsDataProtoBuf.TSDataDouble mergeData(TsDataProtoBuf.TSDataDouble existingData,
                                                  String investmentId, String dpId,
                                                  double value, LocalDate date, CopyOverReasonEnum copyOverReason) {
        long epochDay = date.toEpochDay();

        List<Long> dates = new ArrayList<>(existingData.getDatesList());
        List<Double> values = new ArrayList<>(existingData.getValuesList());
        List<Long> copyOverDates = new ArrayList<>(existingData.getCopyOverDatesList());
        List<Integer> copyOverReasons = new ArrayList<>(existingData.getCopyOverReasonsList());

        int insertIndex = findInsertPosition(dates, epochDay);

        if (insertIndex < dates.size() && dates.get(insertIndex).equals(epochDay)) {
            values.set(insertIndex, value);

            if (copyOverReason != CopyOverReasonEnum.PRICE) {

                int copyOverIndex = copyOverDates.indexOf(epochDay);
                if (copyOverIndex >= 0) {
                    copyOverReasons.set(copyOverIndex, getCopyOverReasonValue(copyOverReason));
                } else {
                    copyOverDates.add(epochDay);
                    copyOverReasons.add(getCopyOverReasonValue(copyOverReason));
                }
            }
        } else {
            dates.add(insertIndex, epochDay);
            values.add(insertIndex, value);

            if (copyOverReason != CopyOverReasonEnum.PRICE) {
                copyOverDates.add(epochDay);
                copyOverReasons.add(getCopyOverReasonValue(copyOverReason));
            }
        }

        TsDataProtoBuf.TSDataDouble.Builder builder = TsDataProtoBuf.TSDataDouble.newBuilder()
                .setInvestmentId(investmentId)
                .setDpId(dpId)
                .addAllDates(dates)
                .addAllValues(values)
                .addAllCopyOverDates(copyOverDates)
                .addAllCopyOverReasons(copyOverReasons);

        return builder.build();
    }

    private int findInsertPosition(List<Long> dates, long targetDate) {
        int left = 0;
        int right = dates.size();

        while (left < right) {
            int mid = left + (right - left) / 2;
            if (dates.get(mid) < targetDate) {
                left = mid + 1;
            } else {
                right = mid;
            }
        }

        return left;
    }

    /**
     * Set hash values in Redis without expiration
     */
    private void setHashValueBatch(byte[] keyBytes, Map<byte[], byte[]> batchData) {
        redisTsRepo.setHashWithoutExpiration(keyBytes, batchData)
                .blockFirst();
    }

    /**
     * Convert BigDecimal to double value
     */
    private double getDoubleValue(BigDecimal bigDecimal) {
        //log if bigDecimal is null
        if (bigDecimal == null) {
            log.debug("MarketPriceDataService: BigDecimal is null, returning 0.0");
            return 0.0;
        }
        return bigDecimal.doubleValue();
    }

    /**
     * Convert CopyOverReasonEnum to numeric value
     */
    private int getCopyOverReasonValue(CopyOverReasonEnum copyOverReason) {
        if (copyOverReason == null) {
            return -1;
        }

        // Convert enum to ordinal value
        return copyOverReason.ordinal();
    }
}
