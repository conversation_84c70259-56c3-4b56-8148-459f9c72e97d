package com.morningstar.martkafkaconsumer.consumer;

import com.morningstar.data.domain.MarketPrice;
import com.morningstar.data.domain.common.DataPointChangeEventList;
import com.morningstar.data.domain.eod.events.MarketPriceDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Slf4j
public class MarketPriceRawConsumer extends  AbstractMessageConsumer<Long, MarketPriceDetail>{
    public MarketPriceRawConsumer(KafkaConsumerEndpoint<Long, MarketPriceDetail> kafkaConsumerEndpoint) {
        super(kafkaConsumerEndpoint);

    }
    @Override
    protected void processMessages(Collection<MarketPriceDetail> messages, String pollId) {
        log.info("MarketPriceRawConsumer Processing {} messages with pollId: {}", messages.size(), pollId);
    }
}
