package com.morningstar.martkafkaconsumer.config;

import com.morningstar.data.domain.eod.events.MarketPriceDetail;
import com.morningstar.data.domain.ics.eod.MarketPriceEnrichedEvent;
import com.morningstar.dp.messaging.common.IKafkaFactory;
import com.morningstar.martkafkaconsumer.consumer.KafkaConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.KafkaPluginConsumerEndpoint;
import com.morningstar.martkafkaconsumer.consumer.MarketPriceEnrichedConsumer;
import com.morningstar.martkafkaconsumer.consumer.MarketPriceRawConsumer;
import com.morningstar.martkafkaconsumer.repository.RedisTsRepo;
import com.morningstar.martkafkaconsumer.service.MarketPriceDataService;
import com.morningstar.martkafkaconsumer.util.RedisUtil;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.Delay;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.specific.SpecificRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@EnableConfigurationProperties({RedisProperties.class, MarketPriceDataConfig.class})
@DependsOn({"market_price_list_updated", "market_price_enriched_event"})
public class MartKafkaConsumerAutoConfiguration<K, V extends SpecificRecord> {

    @Bean("marketPriceRawConsumerEndpoint")
    @ConditionalOnMissingBean(name = "marketPriceRawConsumerEndpoint")
    public KafkaConsumerEndpoint<K, V> marketPriceRawConsumerEndpoint(
            @Qualifier("market_price_list_updated") KafkaConsumerEndpoint<K, V> marketPriceListUpdatedEndpoint) {
        return marketPriceListUpdatedEndpoint;
    }

    @Bean("marketPriceEnrichedConsumerEndpoint")
    @ConditionalOnMissingBean(name = "marketPriceEnrichedConsumerEndpoint")
    public KafkaConsumerEndpoint<K, V> marketPriceEnrichedConsumerEndpoint(
            @Qualifier("market_price_enriched_event") KafkaConsumerEndpoint<K, V> marketPriceEnrichedEventEndpoint) {
        return marketPriceEnrichedEventEndpoint;
    }

    @Bean
    @ConditionalOnMissingBean(name = "marketPriceEnrichedConsumer")
    public MarketPriceEnrichedConsumer marketPriceEnrichedConsumer(
            @Qualifier("marketPriceEnrichedConsumerEndpoint") KafkaConsumerEndpoint<Long, MarketPriceEnrichedEvent> endpoint,
            @Qualifier("marketPriceDataService") MarketPriceDataService marketPriceDataService) {
        return new MarketPriceEnrichedConsumer(endpoint, marketPriceDataService);
    }

    @Bean
    @ConditionalOnMissingBean(name = "marketPriceRawConsumer")
    public MarketPriceRawConsumer marketPriceRawConsumer(@Qualifier("marketPriceRawConsumerEndpoint") KafkaConsumerEndpoint<Long, MarketPriceDetail> endpoint) {
        return new MarketPriceRawConsumer(endpoint);
    }

    @Bean
    @ConditionalOnMissingBean(name = "marketPriceDataService")
    public MarketPriceDataService marketPriceDataService(@Qualifier("tsRepo") RedisTsRepo redisTsRepo,
                                                         MarketPriceDataConfig config) {
        return new MarketPriceDataService(redisTsRepo, config);
    }

    @Bean(name = "tsRepo")
    @ConditionalOnMissingBean(name = "tsRepo")
    public RedisTsRepo tsRepo(ClientResources clientResources, ClusterClientOptions clientOptions, RedisProperties redisProperties) {
        return new RedisTsRepo(RedisUtil.createLettuceConnectionFactories(
                redisProperties.getTsData().getHost(),
                redisProperties.getTsData().getPort(),
                redisProperties.getClientName(),
                10,
                clientResources,
                clientOptions));
    }

    @Bean(name = "clientOptions")
    public ClusterClientOptions clientOptions() {
        return ClusterClientOptions.builder()
                .suspendReconnectOnProtocolFailure(true)
                .autoReconnect(true)
                .build();
    }

    @Bean(name = "clientResources")
    public static ClientResources getClientResources() {
        int cpuCount = Runtime.getRuntime().availableProcessors();
        return DefaultClientResources.builder()
                .ioThreadPoolSize(cpuCount * 2)
                .computationThreadPoolSize(cpuCount)
                .reconnectDelay(Delay.exponential(Duration.ofMillis(100), Duration.ofSeconds(30), 2, TimeUnit.MILLISECONDS))
                .build();
    }

}
